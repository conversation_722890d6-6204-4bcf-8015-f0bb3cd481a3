.autocomplete {
  display: inline-flex;
  width: 140px;

  .MuiAutocomplete-endAdornment {
    top: 0 !important;
    right: 2px !important;
  }

  .MuiInputBase-input {
    font-size: 12px;
  }

  .Mui-disabled {
    svg polyline {
      stroke: $light-color-300;
    }
  }
}

.MuiAutocomplete-popper .MuiAutocomplete-option {
  font-size: 12px !important;
  min-height: 40px !important;
  height: unset !important;
  padding-top: 0 !important;
  padding-bottom: 3px !important;

  &.selected-all-option {
    font-weight: 600 !important;

    .MuiTypography-root {
      font-weight: 600 !important;
      text-transform: none !important;
    }
  }

  &.inverse-selection-option {
    font-weight: 600 !important;
    font-style: italic !important;
    color: $primary-color !important;

    .MuiTypography-root {
      font-weight: 600 !important;
      font-style: italic !important;
      text-transform: none !important;
      color: $primary-color !important;
    }
  }
}

.MuiAutocomplete-listbox {
  .MuiCheckbox-root {
    flex-shrink: 0 !important;
  }
}
