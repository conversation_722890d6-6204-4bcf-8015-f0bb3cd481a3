import getEnableSelectAllConfig, { selectAllOptionConfig, inverseSelectionOptionConfig } from './getEnableSelectAllConfig';

describe('shared: Autocomplete: getEnableSelectAllConfig', () => {
  describe('when optionKey is not provided (simple values)', () => {
    const baseOptions = ['A', 'B', 'C'];
    const selectAllLabel = selectAllOptionConfig.label;

    test('should return optionsWithSelectAll with Select All at the beginning', () => {
      const { optionsWhenEnableSelectAll } = getEnableSelectAllConfig({
        value: [],
        options: baseOptions,
      });

      expect(optionsWhenEnableSelectAll).toEqual([selectAllLabel, ...baseOptions]);
    });

    test('should detect Select All option correctly', () => {
      const { isSelectAllOption } = getEnableSelectAllConfig({ value: [], options: baseOptions });

      expect(isSelectAllOption('Select All')).toBe(true);
      expect(isSelectAllOption('A')).toBe(false);
    });

    test('should return all options when Select All is clicked', () => {
      const { getNewValueWhenEnableSelectAll } = getEnableSelectAllConfig({
        value: ['A'],
        options: baseOptions,
      });

      const result = getNewValueWhenEnableSelectAll(['A', 'Select All']);

      expect(result).toEqual(baseOptions);
    });

    test('should return empty array if Select All is clicked again when all options are already selected', () => {
      const { getNewValueWhenEnableSelectAll } = getEnableSelectAllConfig({
        value: baseOptions,
        options: baseOptions,
      });

      const result = getNewValueWhenEnableSelectAll(['Select All']);

      expect(result).toEqual([]);
    });

    test('should return unchanged value when Select All is not clicked', () => {
      const { getNewValueWhenEnableSelectAll } = getEnableSelectAllConfig({
        value: ['A'],
        options: baseOptions,
      });

      const result = getNewValueWhenEnableSelectAll(['A', 'B']);

      expect(result).toEqual(['A', 'B']);
    });

    test('should set isAllSelected correctly', () => {
      const { isAllSelected } = getEnableSelectAllConfig({
        value: baseOptions,
        options: baseOptions,
      });

      expect(isAllSelected).toBe(true);
    });
  });

  describe('when optionKey is provided (object values)', () => {
    const objectOptions = [
      { id: '1', name: 'A' },
      { id: '2', name: 'B' },
    ];

    const selectAllObj = {
      id: 'select-all',
      name: 'Select All',
    };

    test('should include selectAll option with optionKey', () => {
      const { optionsWhenEnableSelectAll } = getEnableSelectAllConfig({
        value: [],
        options: objectOptions,
        optionKey: 'name',
      });

      expect(optionsWhenEnableSelectAll[0]).toEqual(selectAllObj);
    });

    test('should detect Select All object correctly', () => {
      const { isSelectAllOption } = getEnableSelectAllConfig({
        value: [],
        options: objectOptions,
        optionKey: 'name',
      });

      expect(isSelectAllOption(selectAllObj)).toBe(true);
      expect(isSelectAllOption(objectOptions[0])).toBe(false);
    });

    test('should return all options when Select All is clicked', () => {
      const { getNewValueWhenEnableSelectAll } = getEnableSelectAllConfig({
        value: [objectOptions[0]],
        options: objectOptions,
        optionKey: 'name',
      });

      const result = getNewValueWhenEnableSelectAll([selectAllObj, objectOptions[0]]);

      expect(result).toEqual(objectOptions);
    });

    test('should clear all when deselecting Select All', () => {
      const { getNewValueWhenEnableSelectAll } = getEnableSelectAllConfig({
        value: objectOptions,
        options: objectOptions,
        optionKey: 'name',
      });

      const result = getNewValueWhenEnableSelectAll([selectAllObj]);

      expect(result).toEqual([]);
    });

    test('should return the modifier class for "Select All" when no optionKey is used', () => {
      const options = ['Option 1'];
      const config = getEnableSelectAllConfig({ value: [], optionKey: null, options });

      const className = config.getOptionClassName('Select All');

      expect(className).toBe(selectAllOptionConfig.modifier);
    });

    test('should return an empty string for regular options', () => {
      const options = [{ id: '1', label: 'Option 1' }];
      const config = getEnableSelectAllConfig({ value: [], optionKey: 'label', options });

      const className = config.getOptionClassName(options[0]);

      expect(className).toBe('');
    });
  });

  describe('Inverse Selection functionality', () => {
    const baseOptions = ['A', 'B', 'C', 'D'];

    test('should include inverse selection option when enableInverseSelection is true and has partial selection', () => {
      const { optionsWhenEnableSelectAll } = getEnableSelectAllConfig({
        value: ['A', 'B'],
        options: baseOptions,
        enableInverseSelection: true,
      });

      expect(optionsWhenEnableSelectAll).toEqual([
        'Select All',
        'Inverse Selection',
        ...baseOptions,
      ]);
    });

    test('should not include inverse selection option when no items are selected', () => {
      const { optionsWhenEnableSelectAll } = getEnableSelectAllConfig({
        value: [],
        options: baseOptions,
        enableInverseSelection: true,
      });

      expect(optionsWhenEnableSelectAll).toEqual(['Select All', ...baseOptions]);
    });

    test('should not include inverse selection option when all items are selected', () => {
      const { optionsWhenEnableSelectAll } = getEnableSelectAllConfig({
        value: baseOptions,
        options: baseOptions,
        enableInverseSelection: true,
      });

      expect(optionsWhenEnableSelectAll).toEqual(['Select All', ...baseOptions]);
    });

    test('should detect inverse selection option correctly', () => {
      const { isInverseSelectionOption } = getEnableSelectAllConfig({
        value: ['A'],
        options: baseOptions,
        enableInverseSelection: true,
      });

      expect(isInverseSelectionOption('Inverse Selection')).toBe(true);
      expect(isInverseSelectionOption('A')).toBe(false);
    });

    test('should return inverted selection when inverse selection is clicked', () => {
      const { getNewValueWhenEnableSelectAll } = getEnableSelectAllConfig({
        value: ['A', 'B'],
        options: baseOptions,
        enableInverseSelection: true,
      });

      const result = getNewValueWhenEnableSelectAll(['A', 'B', 'Inverse Selection']);

      expect(result).toEqual(['C', 'D']);
    });

    test('should work with object options and optionKey', () => {
      const objectOptions = [
        { id: '1', name: 'A' },
        { id: '2', name: 'B' },
        { id: '3', name: 'C' },
      ];

      const { getNewValueWhenEnableSelectAll } = getEnableSelectAllConfig({
        value: [objectOptions[0]],
        options: objectOptions,
        optionKey: 'name',
        enableInverseSelection: true,
      });

      const inverseSelectionObj = {
        id: 'inverse-selection',
        name: 'Inverse Selection',
      };

      const result = getNewValueWhenEnableSelectAll([objectOptions[0], inverseSelectionObj]);

      expect(result).toEqual([objectOptions[1], objectOptions[2]]);
    });

    test('should return correct modifier class for inverse selection option', () => {
      const { getOptionClassName } = getEnableSelectAllConfig({
        value: ['A'],
        options: baseOptions,
        enableInverseSelection: true,
      });

      const className = getOptionClassName('Inverse Selection');

      expect(className).toBe(inverseSelectionOptionConfig.modifier);
    });
  });
});
