export const selectAllOptionConfig = {
  id: 'select-all',
  label: 'Select All',
  modifier: 'selected-all-option',
};

export const inverseSelectionOptionConfig = {
  id: 'inverse-selection',
  label: 'Inverse Selection',
  modifier: 'inverse-selection-option',
};

const getEnableSelectAllConfig = ({
  value, optionKey, options, enableInverseSelection = false,
}) => {
  const isAllSelected = value?.length === options?.length;
  const hasPartialSelection = value?.length > 0 && !isAllSelected;

  const selectAllOption = optionKey
    ? { id: selectAllOptionConfig.id, [optionKey]: selectAllOptionConfig.label }
    : selectAllOptionConfig.label;
  const inverseSelectionOption = optionKey
    ? { id: inverseSelectionOptionConfig.id, [optionKey]: inverseSelectionOptionConfig.label }
    : inverseSelectionOptionConfig.label;

  const isSelectAllOption = (option) => (optionKey
    ? option?.id === selectAllOption.id
    : option === selectAllOptionConfig.label);
  const isInverseSelectionOption = (option) => (optionKey
    ? option?.id === inverseSelectionOption.id
    : option === inverseSelectionOptionConfig.label);

  // Build options array - include inverse selection only when there's partial selection
  const specialOptions = [selectAllOption];
  if (enableInverseSelection && hasPartialSelection) {
    specialOptions.push(inverseSelectionOption);
  }

  // Debug logging (remove in production)
  if (enableInverseSelection) {
    console.log('Inverse Selection Debug:', {
      enableInverseSelection,
      hasPartialSelection,
      valueLength: value?.length,
      optionsLength: options?.length,
      isAllSelected,
      willShowInverseSelection: enableInverseSelection && hasPartialSelection,
    });
  }

  const optionsWhenEnableSelectAll = [...specialOptions, ...options];

  const getNewValueWhenEnableSelectAll = (newValues) => {
    const isSelectAllClicked = newValues?.some(isSelectAllOption);
    const isInverseSelectionClicked = newValues?.some(isInverseSelectionOption);

    if (isSelectAllClicked) {
      const currentRegularSelected = value.filter((item) => (
        !isSelectAllOption(item) && !isInverseSelectionOption(item)
      ));
      const unselectClicked = currentRegularSelected.length === options.length;

      return unselectClicked ? [] : options;
    }

    if (isInverseSelectionClicked) {
      // Inverse selection: select all options that are NOT currently selected
      const currentRegularSelected = value.filter((item) => (
        !isSelectAllOption(item) && !isInverseSelectionOption(item)
      ));
      const unselectedOptions = options.filter((option) => (
        !currentRegularSelected.some((selected) => (
          optionKey ? selected[optionKey] === option[optionKey] : selected === option
        ))
      ));

      return unselectedOptions;
    }

    return newValues;
  };

  const getOptionClassName = (option) => {
    const isSelectAllLabel = option[optionKey] === selectAllOptionConfig.label
      || option === selectAllOptionConfig.label;
    const isInverseSelectionLabel = option[optionKey] === inverseSelectionOptionConfig.label
      || option === inverseSelectionOptionConfig.label;

    if (isSelectAllLabel) {
      return selectAllOptionConfig.modifier;
    }
    if (isInverseSelectionLabel) {
      return inverseSelectionOptionConfig.modifier;
    }
    return '';
  };

  return {
    isSelectAllOption,
    isInverseSelectionOption,
    isAllSelected,
    hasPartialSelection,
    optionsWhenEnableSelectAll,
    getNewValueWhenEnableSelectAll,
    getOptionClassName,
  };
};

export default getEnableSelectAllConfig;
